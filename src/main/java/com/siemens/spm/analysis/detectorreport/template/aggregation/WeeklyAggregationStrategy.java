package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Strategy for weekly aggregation of detector reports.
 * Processes data in weekly intervals for days specified in the template.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
public class WeeklyAggregationStrategy extends BaseAggregationStrategy {

    private static final int DAY_INTERVAL = 1;

    public WeeklyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(
            DetectorTemplate template,
            IntersectionInternalVO intersection,
            List<LocalDateTime> targetDateTimes) {

        log.info("Starting weekly aggregation for {} target date times", targetDateTimes.size());

        // Validate template configuration
        if (isEmptyWeekDays(template.getWeekDays())) {
            log.warn("No week days specified in template, returning empty result");
            return createEmptyResultList();
        }

        // Extract common parameters
        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        Integer agencyId = template.getAgencyId();
        String intersectionId = intersection.getId();

        // Group target dates by week and process each week
        Map<LocalDate, List<LocalDateTime>> weeklyGroupedDates = groupTargetDatesByWeek(targetDateTimes);
        List<DetectorReportChartVO> weeklyReports = new ArrayList<>();

        for (Map.Entry<LocalDate, List<LocalDateTime>> weekEntry : weeklyGroupedDates.entrySet()) {
            LocalDate weekStart = weekEntry.getKey();
            List<LocalDateTime> weekTargetDates = weekEntry.getValue();

            log.info("Processing week starting {}, covering {} target dates", weekStart, weekTargetDates.size());

            processWeeklyPeriod(template, weekStart, filterDetectorIds, agencyId, intersectionId)
                    .ifPresent(weeklyReports::add);
        }

        log.info("Completed weekly aggregation, generated {} weekly reports", weeklyReports.size());
        return weeklyReports;
    }

    /**
     * Groups target date times by week (Monday to Sunday).
     * Each week is represented by its Monday date as the key.
     *
     * @param targetDateTimes The list of target date times to group
     * @return Map where key is the Monday of each week, value is list of target dates in that week
     */
    private Map<LocalDate, List<LocalDateTime>> groupTargetDatesByWeek(List<LocalDateTime> targetDateTimes) {
        log.debug("Grouping {} target dates by week", targetDateTimes.size());

        Map<LocalDate, List<LocalDateTime>> weeklyGroups = targetDateTimes.stream()
                .distinct()
                .collect(Collectors.groupingBy(
                        dateTime -> getWeekStart(dateTime.toLocalDate()),
                        TreeMap::new,
                        Collectors.toList()
                ));

        log.debug("Grouped target dates into {} weeks", weeklyGroups.size());
        return weeklyGroups;
    }

    /**
     * Processes a single weekly period, collecting and aggregating daily reports for all applicable days.
     * Only processes days that are specified in the template's week days configuration.
     *
     * @param template          The detector template containing configuration
     * @param weekStart         The Monday date of the week to process
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return Optional containing the aggregated weekly report if data exists, empty otherwise
     */
    private Optional<DetectorReportChartVO> processWeeklyPeriod(
            DetectorTemplate template,
            LocalDate weekStart,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        LocalDate weekEnd = weekStart.with(DayOfWeek.SUNDAY);
        log.debug("Processing weekly period from {} to {}", weekStart, weekEnd);

        List<DetectorReportChartVO> dailyReportsInWeek = collectDailyReportsForWeek(
                template, weekStart, weekEnd, filterDetectorIds, agencyId, intersectionId);

        if (dailyReportsInWeek.isEmpty()) {
            log.debug("No data found for week {} to {} after applying week days filter", weekStart, weekEnd);
            return Optional.empty();
        }

        log.debug("Aggregating {} daily reports for week {} to {}",
                dailyReportsInWeek.size(), weekStart, weekEnd);

        DetectorReportChartVO weeklyAggregatedReport = combinePeriodReports(dailyReportsInWeek);
        calculateWeeklyMetrics(weeklyAggregatedReport);
        setWeeklyReportTimeBoundaries(weeklyAggregatedReport, weekStart, weekEnd);

        log.info("Successfully created weekly report for {} to {} with {} days of data",
                weekStart, weekEnd, dailyReportsInWeek.size());

        return Optional.of(weeklyAggregatedReport);
    }

    /**
     * Collects daily detector reports for each applicable day in the week.
     * Only processes days that are specified in the template's week days configuration.
     *
     * @param template          The detector template containing week days configuration
     * @param weekStart         The start date of the week (Monday)
     * @param weekEnd           The end date of the week (Sunday)
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return List of daily reports for applicable days in the week
     */
    private List<DetectorReportChartVO> collectDailyReportsForWeek(
            DetectorTemplate template,
            LocalDate weekStart,
            LocalDate weekEnd,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> dailyReports = new ArrayList<>();
        Set<DayOfWeek> configuredWeekDays = template.getWeekDays();
        LocalDate currentDate = weekStart;

        log.debug("Collecting daily reports for week {} to {}, configured days: {}",
                weekStart, weekEnd, configuredWeekDays);

        while (!currentDate.isAfter(weekEnd)) {
            DayOfWeek currentDayOfWeek = currentDate.getDayOfWeek();

            if (configuredWeekDays.contains(currentDayOfWeek)) {
                log.debug("Processing {} ({})", currentDate, currentDayOfWeek);

                Optional<DetectorReportChartVO> dailyReport = collectDailyReportForDate(
                        template, currentDate, filterDetectorIds, agencyId, intersectionId);

                if (dailyReport.isPresent()) {
                    dailyReports.add(dailyReport.get());
                    log.debug("Added daily report for {} with data", currentDate);
                }
            } else {
                log.debug("Skipping {} ({}) - not in configured week days", currentDate, currentDayOfWeek);
            }

            currentDate = currentDate.plusDays(DAY_INTERVAL);
        }

        log.debug("Collected {} daily reports for week {} to {}", dailyReports.size(), weekStart, weekEnd);
        return dailyReports;
    }

    /**
     * Collects detector report data for a single date.
     * Handles potential errors gracefully and provides detailed logging.
     *
     * @param template          The detector template containing time configuration
     * @param date              The date to collect data for
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return Optional containing the daily report if successful, empty if no data or error
     */
    private Optional<DetectorReportChartVO> collectDailyReportForDate(
            DetectorTemplate template,
            LocalDate date,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        try {
            LocalDateTime fromTime = template.getStartTime().atDate(date);
            LocalDateTime toTime = template.getEndTime().atDate(date);

            log.debug("Analyzing detector data for {} ({}) from {} to {}",
                    date, date.getDayOfWeek(), fromTime, toTime);

            Optional<DetectorReportChartVO> result = detectorReportAnalysis(
                    agencyId, intersectionId, fromTime, toTime, filterDetectorIds);

            if (result.isEmpty()) {
                log.debug("No detector data found for {} ({})", date, date.getDayOfWeek());
            }

            return result;

        } catch (Exception e) {
            log.warn("Error collecting daily report for {} ({}): {}",
                    date, date.getDayOfWeek(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Calculates weekly metrics for all detectors and phases in the aggregated report.
     * This method triggers the weekly calculation logic for each phase data.
     *
     * @param report The aggregated weekly report to calculate metrics for
     */
    private void calculateWeeklyMetrics(DetectorReportChartVO report) {
        if (report.getDetectorDataMap() == null) {
            log.warn("No detector data map found in report, skipping weekly metrics calculation");
            return;
        }

        log.debug("Calculating weekly metrics for {} detectors", report.getDetectorDataMap().size());

        report.getDetectorDataMap().forEach((detectorId, detectorData) -> {
            if (detectorData.getDetectorPhaseDataMap() != null) {
                detectorData.getDetectorPhaseDataMap().forEach((phaseId, phaseData) -> {
                    log.debug("Calculating weekly metrics for detector {} phase {}", detectorId, phaseId);
                    phaseData.calculateReportWeekly();
                });
            }
        });

        log.debug("Completed weekly metrics calculation");
    }

    /**
     * Sets the time boundaries for the weekly aggregated report.
     * The report covers from the start of the week (Monday 00:00) to the end of the week (Sunday 23:59:59).
     *
     * @param report    The weekly report to set boundaries for
     * @param weekStart The Monday date of the week
     * @param weekEnd   The Sunday date of the week
     */
    private void setWeeklyReportTimeBoundaries(
            DetectorReportChartVO report,
            LocalDate weekStart,
            LocalDate weekEnd) {

        LocalDateTime fromTime = weekStart.atStartOfDay();
        LocalDateTime toTime = weekEnd.plusDays(1).atStartOfDay();

        report.setFromTime(fromTime);
        report.setToTime(toTime);

        log.debug("Set weekly report time boundaries: {} to {}", fromTime, toTime);
    }

    /**
     * Gets the start of the week (Monday) for a given date.
     * This is a utility method used for consistent week boundary calculation.
     *
     * @param date The date to find the week start for
     * @return The Monday of the week containing the given date
     */
    private LocalDate getWeekStart(LocalDate date) {
        return date.with(DayOfWeek.MONDAY);
    }

    /**
     * Groups a list of day periods into weekly periods (Monday to Sunday).
     * Each day period is represented as a pair of LocalDateTime objects (start and end times).
     * This method is useful for processing complex period arrangements.
     *
     * @param dayPeriods List of day periods where each period is defined by start and end LocalDateTime
     * @return List of weekly buckets, where each bucket contains periods for one week (Monday to Sunday)
     */
    public List<List<Pair<LocalDateTime, LocalDateTime>>> groupDayPeriodsIntoWeeklyPeriods(
            List<Pair<LocalDateTime, LocalDateTime>> dayPeriods) {

        if (dayPeriods == null || dayPeriods.isEmpty()) {
            log.debug("No day periods to group into weekly periods");
            return new ArrayList<>();
        }

        log.debug("Grouping {} day periods into weekly periods", dayPeriods.size());

        // First, split any periods that span multiple days into single-day periods
        List<Pair<LocalDateTime, LocalDateTime>> normalizedPeriods = splitMultiDayPeriods(dayPeriods);

        Map<LocalDate, List<Pair<LocalDateTime, LocalDateTime>>> weeklyGroups = normalizedPeriods.stream()
                .collect(Collectors.groupingBy(
                        period -> getWeekStart(period.getFirst().toLocalDate()),
                        TreeMap::new,
                        Collectors.toList()
                ));

        List<List<Pair<LocalDateTime, LocalDateTime>>> weeklyPeriods = new ArrayList<>();
        for (Map.Entry<LocalDate, List<Pair<LocalDateTime, LocalDateTime>>> entry : weeklyGroups.entrySet()) {
            LocalDate weekStart = entry.getKey();
            List<Pair<LocalDateTime, LocalDateTime>> weekPeriods = entry.getValue();
            weekPeriods.sort(Comparator.comparing(Pair::getFirst));
            weeklyPeriods.add(weekPeriods);
            log.debug("Week starting {} contains {} periods", weekStart, weekPeriods.size());
        }

        log.debug("Grouped day periods into {} weekly periods", weeklyPeriods.size());
        return weeklyPeriods;
    }

    /**
     * Splits periods that span multiple days into single-day periods.
     * This handles edge cases where a period crosses midnight or spans multiple days.
     * Provides robust error handling for invalid period data.
     *
     * @param dayPeriods Original list of day periods that may span multiple days
     * @return List of normalized periods where each period is within a single day
     */
    private List<Pair<LocalDateTime, LocalDateTime>> splitMultiDayPeriods(
            List<Pair<LocalDateTime, LocalDateTime>> dayPeriods) {

        List<Pair<LocalDateTime, LocalDateTime>> normalizedPeriods = new ArrayList<>();

        for (Pair<LocalDateTime, LocalDateTime> period : dayPeriods) {
            LocalDateTime start = period.getFirst();
            LocalDateTime end = period.getSecond();

            if (start.isAfter(end)) {
                log.warn("Skipping period with start time after end time: start={}, end={}", start, end);
                continue;
            }

            if (start.toLocalDate().equals(end.toLocalDate())) {
                normalizedPeriods.add(period);
            } else {
                List<Pair<LocalDateTime, LocalDateTime>> splitPeriods = splitPeriodAcrossDays(start, end);
                normalizedPeriods.addAll(splitPeriods);
                log.debug("Split multi-day period {} to {} into {} daily periods",
                        start, end, splitPeriods.size());
            }
        }
        return normalizedPeriods;
    }

    /**
     * Splits a period that spans multiple days into separate daily periods.
     * Each resulting period represents one day's worth of data.
     *
     * @param start Start time of the multi-day period
     * @param end   End time of the multi-day period
     * @return List of daily periods, each contained within a single day
     */
    private List<Pair<LocalDateTime, LocalDateTime>> splitPeriodAcrossDays(
            LocalDateTime start, LocalDateTime end) {

        List<Pair<LocalDateTime, LocalDateTime>> dailyPeriods = new ArrayList<>();
        LocalDateTime currentStart = start;

        while (currentStart.toLocalDate().isBefore(end.toLocalDate())) {
            LocalDateTime currentEnd = currentStart.toLocalDate().plusDays(1).atStartOfDay();
            dailyPeriods.add(Pair.of(currentStart, currentEnd));

            currentStart = currentEnd;
        }

        if (!currentStart.isAfter(end)) {
            dailyPeriods.add(Pair.of(currentStart, end));
        }

        log.debug("Split period from {} to {} into {} daily periods", start, end, dailyPeriods.size());
        return dailyPeriods;
    }

}
