package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Strategy for weekly aggregation of detector reports.
 * Processes data in weekly intervals for days specified in the template.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
public class WeeklyAggregationStrategy extends BaseAggregationStrategy {

    private static final int DAY_INTERVAL = 1;

    public WeeklyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(
            DetectorTemplate template,
            IntersectionInternalVO intersection,
            List<LocalDateTime> targetDateTimes) {

        log.info("Starting weekly aggregation for {} target date times", targetDateTimes.size());

        if (isEmptyWeekDays(template.getWeekDays())) {
            log.warn("No week days specified in template, returning empty result");
            return createEmptyResultList();
        }

        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        Integer agencyId = template.getAgencyId();
        String intersectionId = intersection.getId();

        Map<LocalDate, List<LocalDateTime>> weeklyGroupedDates = groupTargetDatesByWeek(targetDateTimes);
        List<DetectorReportChartVO> weeklyReports = new ArrayList<>();

        for (Map.Entry<LocalDate, List<LocalDateTime>> weekEntry : weeklyGroupedDates.entrySet()) {
            LocalDate weekStart = weekEntry.getKey();
            List<LocalDateTime> weekTargetDates = weekEntry.getValue();

            log.info("Processing week starting {}, covering {} target dates", weekStart, weekTargetDates.size());

            processWeeklyPeriod(template, weekStart, weekTargetDates, filterDetectorIds, agencyId, intersectionId)
                    .ifPresent(weeklyReports::add);
        }

        log.info("Completed weekly aggregation, generated {} weekly reports", weeklyReports.size());
        return weeklyReports;
    }

    /**
     * Groups target date times by week (Monday to Sunday).
     * Each week is represented by its Monday date as the key.
     *
     * @param targetDateTimes The list of target date times to group
     * @return Map where key is the Monday of each week, value is list of target dates in that week
     */
    private Map<LocalDate, List<LocalDateTime>> groupTargetDatesByWeek(List<LocalDateTime> targetDateTimes) {
        log.debug("Grouping {} target dates by week", targetDateTimes.size());
        Map<LocalDate, List<LocalDateTime>> weeklyGroups = targetDateTimes.stream()
                .distinct()
                .collect(Collectors.groupingBy(dateTime -> dateTime.toLocalDate().with(DayOfWeek.MONDAY),
                        TreeMap::new,
                        Collectors.toList()

                ));

        log.debug("Grouped target dates into {} weeks", weeklyGroups.size());
        return weeklyGroups;
    }

    /**
     * Parses the target date times list into daily time period pairs.
     * The targetDateTimes follows a pattern where each date has exactly 2 consecutive entries:
     * start time followed by end time.
     *
     * @param targetDateTimes List of target date times in pairs (start, end, start, end, ...)
     * @return Map where key is the date, value is the time period pair for that date
     */
    private Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> parseDailyTimePairs(List<LocalDateTime> targetDateTimes) {
        Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> dailyPairs = new HashMap<>();

        log.debug("Parsing {} target date times into daily time pairs", targetDateTimes.size());

        // Sort the target date times to ensure proper pairing
        List<LocalDateTime> sortedTimes = targetDateTimes.stream()
                .sorted()
                .collect(Collectors.toList());

        // Group by date first
        Map<LocalDate, List<LocalDateTime>> timesByDate = sortedTimes.stream()
                .collect(Collectors.groupingBy(LocalDateTime::toLocalDate));

        // Create pairs for each date
        for (Map.Entry<LocalDate, List<LocalDateTime>> entry : timesByDate.entrySet()) {
            LocalDate date = entry.getKey();
            List<LocalDateTime> timesForDate = entry.getValue().stream()
                    .sorted()
                    .collect(Collectors.toList());

            if (timesForDate.size() >= 2) {
                // Use first and last times as start and end
                LocalDateTime startTime = timesForDate.get(0);
                LocalDateTime endTime = timesForDate.get(timesForDate.size() - 1);
                dailyPairs.put(date, Pair.of(startTime, endTime));

                log.debug("Created time pair for {}: {} to {}", date, startTime, endTime);
            } else if (timesForDate.size() == 1) {
                log.warn("Only one time found for date {}: {}. Skipping this date.", date, timesForDate.get(0));
            }
        }

        log.debug("Created {} daily time pairs from {} target dates", dailyPairs.size(), targetDateTimes.size());
        return dailyPairs;
    }

    /**
     * Processes a single weekly period, collecting and aggregating daily reports for all applicable days.
     * Only processes days that are specified in the template's week days configuration.
     *
     * @param template          The detector template containing configuration
     * @param weekStart         The Monday date of the week to process
     * @param weekTargetDates   The target date times for this specific week
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return Optional containing the aggregated weekly report if data exists, empty otherwise
     */
    private Optional<DetectorReportChartVO> processWeeklyPeriod(
            DetectorTemplate template,
            LocalDate weekStart,
            List<LocalDateTime> weekTargetDates,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        LocalDate weekEnd = weekStart.with(DayOfWeek.SUNDAY);
        log.debug("Processing weekly period from {} to {} with {} target dates",
                weekStart, weekEnd, weekTargetDates.size());

        List<DetectorReportChartVO> dailyReportsInWeek = collectDailyReportsForWeek(
                template, weekStart, weekEnd, weekTargetDates, filterDetectorIds, agencyId, intersectionId);

        if (dailyReportsInWeek.isEmpty()) {
            log.debug("No data found for week {} to {} after applying week days filter", weekStart, weekEnd);
            return Optional.empty();
        }

        log.debug("Aggregating {} daily reports for week {} to {}",
                dailyReportsInWeek.size(), weekStart, weekEnd);

        DetectorReportChartVO weeklyAggregatedReport = combinePeriodReports(dailyReportsInWeek);
        calculateWeeklyMetrics(weeklyAggregatedReport);
        setWeeklyReportTimeBoundaries(weeklyAggregatedReport, weekStart, weekEnd);

        log.info("Successfully created weekly report for {} to {} with {} days of data",
                weekStart, weekEnd, dailyReportsInWeek.size());

        return Optional.of(weeklyAggregatedReport);
    }

    /**
     * Collects daily detector reports for each applicable day in the week.
     * Only processes days that are specified in the template's week days configuration.
     * Uses actual time ranges from targetDateTimes instead of template's generic times.
     *
     * @param template          The detector template containing week days configuration
     * @param weekStart         The start date of the week (Monday)
     * @param weekEnd           The end date of the week (Sunday)
     * @param weekTargetDates   The target date times for this specific week
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return List of daily reports for applicable days in the week
     */
    private List<DetectorReportChartVO> collectDailyReportsForWeek(
            DetectorTemplate template,
            LocalDate weekStart,
            LocalDate weekEnd,
            List<LocalDateTime> weekTargetDates,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> dailyReports = new ArrayList<>();
        Set<DayOfWeek> configuredWeekDays = template.getWeekDays();
        LocalDate currentDate = weekStart;

        log.debug("Collecting daily reports for week {} to {}, configured days: {}, target dates: {}",
                weekStart, weekEnd, configuredWeekDays, weekTargetDates.size());

        while (!currentDate.isAfter(weekEnd)) {
            DayOfWeek currentDayOfWeek = currentDate.getDayOfWeek();

            if (configuredWeekDays.contains(currentDayOfWeek)) {
                log.debug("Processing {} ({})", currentDate, currentDayOfWeek);

                Optional<DetectorReportChartVO> dailyReport = collectDailyReportForDate(
                        currentDate, weekTargetDates, filterDetectorIds, agencyId, intersectionId);

                if (dailyReport.isPresent()) {
                    dailyReports.add(dailyReport.get());
                    log.debug("Added daily report for {} with data", currentDate);
                }
            } else {
                log.debug("Skipping {} ({}) - not in configured week days", currentDate, currentDayOfWeek);
            }

            currentDate = currentDate.plusDays(DAY_INTERVAL);
        }

        log.debug("Collected {} daily reports for week {} to {}", dailyReports.size(), weekStart, weekEnd);
        return dailyReports;
    }

    /**
     * Collects detector report data for a single date using actual time ranges from targetDateTimes.
     * Extracts the specific start and end times for the given date from the targetDateTimes list.
     * Handles potential errors gracefully and provides detailed logging.
     *
     * @param date              The date to collect data for
     * @param weekTargetDates   The target date times for this week containing actual time ranges
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return Optional containing the daily report if successful, empty if no data or error
     */
    private Optional<DetectorReportChartVO> collectDailyReportForDate(
            LocalDate date,
            List<LocalDateTime> weekTargetDates,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        try {
            // Extract actual time ranges for this specific date from weekTargetDates
            List<LocalDateTime> dateSpecificTimes = extractTimeRangesForDate(date, weekTargetDates);

            if (dateSpecificTimes.isEmpty()) {
                log.debug("No target times found for {} ({}) in weekTargetDates, skipping", date, date.getDayOfWeek());
                return Optional.empty();
            }

            // Use the first and last times for this date as the range
            LocalDateTime fromTime = dateSpecificTimes.get(0);
            LocalDateTime toTime = dateSpecificTimes.get(dateSpecificTimes.size() - 1);

            log.debug("Analyzing detector data for {} ({}) from {} to {} (extracted from target times)",
                    date, date.getDayOfWeek(), fromTime, toTime);

            Optional<DetectorReportChartVO> result = detectorReportAnalysis(
                    agencyId, intersectionId, fromTime, toTime, filterDetectorIds);

            if (result.isEmpty()) {
                log.debug("No detector data found for {} ({})", date, date.getDayOfWeek());
            }

            return result;

        } catch (Exception e) {
            log.warn("Error collecting daily report for {} ({}): {}",
                    date, date.getDayOfWeek(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Extracts time ranges for a specific date from the list of target date times.
     * The targetDateTimes list contains pairs of start and end times for each date.
     *
     * @param date            The date to extract time ranges for
     * @param weekTargetDates The list of target date times for the week
     * @return List of LocalDateTime objects for the specified date, sorted chronologically
     */
    private List<LocalDateTime> extractTimeRangesForDate(LocalDate date, List<LocalDateTime> weekTargetDates) {
        List<LocalDateTime> dateSpecificTimes = weekTargetDates.stream()
                .filter(dateTime -> dateTime.toLocalDate().equals(date))
                .sorted()
                .toList();

        log.debug("Extracted {} time ranges for date {}: {}",
                dateSpecificTimes.size(), date, dateSpecificTimes);

        return dateSpecificTimes;
    }

    /**
     * Calculates weekly metrics for all detectors and phases in the aggregated report.
     * This method triggers the weekly calculation logic for each phase data.
     *
     * @param report The aggregated weekly report to calculate metrics for
     */
    private void calculateWeeklyMetrics(DetectorReportChartVO report) {
        if (report.getDetectorDataMap() == null) {
            log.warn("No detector data map found in report, skipping weekly metrics calculation");
            return;
        }

        log.debug("Calculating weekly metrics for {} detectors", report.getDetectorDataMap().size());

        report.getDetectorDataMap().forEach((detectorId, detectorData) -> {
            if (detectorData.getDetectorPhaseDataMap() != null) {
                detectorData.getDetectorPhaseDataMap().forEach((phaseId, phaseData) -> {
                    log.debug("Calculating weekly metrics for detector {} phase {}", detectorId, phaseId);
                    phaseData.calculateReportWeekly();
                });
            }
        });

        log.debug("Completed weekly metrics calculation");
    }

    /**
     * Sets the time boundaries for the weekly aggregated report.
     * The report covers from the start of the week (Monday 00:00) to the end of the week (Sunday 23:59:59).
     *
     * @param report    The weekly report to set boundaries for
     * @param weekStart The Monday date of the week
     * @param weekEnd   The Sunday date of the week
     */
    private void setWeeklyReportTimeBoundaries(
            DetectorReportChartVO report,
            LocalDate weekStart,
            LocalDate weekEnd) {

        LocalDateTime fromTime = weekStart.atStartOfDay();
        LocalDateTime toTime = weekEnd.plusDays(1).atStartOfDay();

        report.setFromTime(fromTime);
        report.setToTime(toTime);

        log.debug("Set weekly report time boundaries: {} to {}", fromTime, toTime);
    }

}
