package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Strategy for weekly aggregation of detector reports.
 * Processes data in weekly intervals for days specified in the template.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
public class WeeklyAggregationStrategy extends BaseAggregationStrategy {

    private static final int DAY_INTERVAL = 1;

    public WeeklyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(
            DetectorTemplate template,
            IntersectionInternalVO intersection,
            List<LocalDateTime> targetDateTimes) {

        log.info("Starting weekly aggregation for {} target date times", targetDateTimes.size());

        if (isEmptyWeekDays(template.getWeekDays())) {
            log.warn("No week days specified in template, returning empty result");
            return createEmptyResultList();
        }

        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        Integer agencyId = template.getAgencyId();
        String intersectionId = intersection.getId();

        // First: Parse target date times into daily time pairs
        Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> dailyTimePairs = parseDailyTimePairs(targetDateTimes);

        // Then: Group daily pairs by week and process each week
        Map<LocalDate, Map<LocalDate, Pair<LocalDateTime, LocalDateTime>>> weeklyGroupedPairs =
                groupDailyPairsByWeek(dailyTimePairs);
        List<DetectorReportChartVO> weeklyReports = new ArrayList<>();

        for (Map.Entry<LocalDate, Map<LocalDate, Pair<LocalDateTime, LocalDateTime>>> weekEntry : weeklyGroupedPairs.entrySet()) {
            LocalDate weekStart = weekEntry.getKey();
            Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> weekDailyPairs = weekEntry.getValue();

            log.info("Processing week starting {}, covering {} daily pairs", weekStart, weekDailyPairs.size());

            processWeeklyPeriod(template, weekStart, weekDailyPairs, filterDetectorIds, agencyId, intersectionId)
                    .ifPresent(weeklyReports::add);
        }

        log.info("Completed weekly aggregation, generated {} weekly reports", weeklyReports.size());
        return weeklyReports;
    }

    /**
     * Groups daily time pairs by week (Monday to Sunday).
     * Each week is represented by its Monday date as the key.
     *
     * @param dailyTimePairs Map of daily time pairs to group by week
     * @return Map where key is the Monday of each week, value is map of daily pairs for that week
     */
    private Map<LocalDate, Map<LocalDate, Pair<LocalDateTime, LocalDateTime>>> groupDailyPairsByWeek(
            Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> dailyTimePairs) {

        log.debug("Grouping {} daily time pairs by week", dailyTimePairs.size());

        Map<LocalDate, Map<LocalDate, Pair<LocalDateTime, LocalDateTime>>> weeklyGroups = new TreeMap<>();

        for (Map.Entry<LocalDate, Pair<LocalDateTime, LocalDateTime>> entry : dailyTimePairs.entrySet()) {
            LocalDate date = entry.getKey();
            Pair<LocalDateTime, LocalDateTime> timePair = entry.getValue();
            LocalDate weekStart = getWeekStart(date);

            // Get or create the week's daily pairs map
            weeklyGroups.computeIfAbsent(weekStart, k -> new HashMap<>()).put(date, timePair);
        }

        log.debug("Grouped daily pairs into {} weeks", weeklyGroups.size());
        return weeklyGroups;
    }

    /**
     * Parses the target date times list into daily time period pairs.
     * The targetDateTimes follows a pattern where each date has exactly 2 consecutive entries:
     * start time followed by end time.
     *
     * @param targetDateTimes List of target date times in pairs (start, end, start, end, ...)
     * @return Map where key is the date, value is the time period pair for that date
     */
    private Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> parseDailyTimePairs(List<LocalDateTime> targetDateTimes) {
        Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> dailyPairs = new HashMap<>();

        log.debug("Parsing {} target date times into daily time pairs", targetDateTimes.size());

        // Sort the target date times to ensure proper pairing
        List<LocalDateTime> sortedTimes = targetDateTimes.stream()
                .sorted()
                .toList();

        // Group by date first
        Map<LocalDate, List<LocalDateTime>> timesByDate = sortedTimes.stream()
                .collect(Collectors.groupingBy(LocalDateTime::toLocalDate));

        // Create pairs for each date
        for (Map.Entry<LocalDate, List<LocalDateTime>> entry : timesByDate.entrySet()) {
            LocalDate date = entry.getKey();
            List<LocalDateTime> timesForDate = entry.getValue().stream()
                    .sorted()
                    .toList();

            if (timesForDate.size() >= 2) {
                LocalDateTime startTime = timesForDate.get(0);
                LocalDateTime endTime = timesForDate.get(timesForDate.size() - 1);
                dailyPairs.put(date, Pair.of(startTime, endTime));

                log.debug("Created time pair for {}: {} to {}", date, startTime, endTime);
            } else if (timesForDate.size() == 1) {
                log.warn("Only one time found for date {}: {}. Skipping this date.", date, timesForDate.get(0));
            }
        }

        log.debug("Created {} daily time pairs from {} target dates", dailyPairs.size(), targetDateTimes.size());
        return dailyPairs;
    }

    /**
     * Processes a single weekly period, collecting and aggregating daily reports for all applicable days.
     * Only processes days that are specified in the template's week days configuration.
     *
     * @param template          The detector template containing configuration
     * @param weekStart         The Monday date of the week to process
     * @param dailyTimePairs    Map of daily time period pairs for all dates
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return Optional containing the aggregated weekly report if data exists, empty otherwise
     */
    private Optional<DetectorReportChartVO> processWeeklyPeriod(
            DetectorTemplate template,
            LocalDate weekStart,
            Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> dailyTimePairs,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        LocalDate weekEnd = weekStart.with(DayOfWeek.SUNDAY);
        log.debug("Processing weekly period from {} to {} with {} available daily time pairs",
                weekStart, weekEnd, dailyTimePairs.size());

        List<DetectorReportChartVO> dailyReportsInWeek = collectDailyReportsForWeek(
                template, weekStart, weekEnd, dailyTimePairs, filterDetectorIds, agencyId, intersectionId);

        if (dailyReportsInWeek.isEmpty()) {
            log.debug("No data found for week {} to {} after applying week days filter", weekStart, weekEnd);
            return Optional.empty();
        }

        log.debug("Aggregating {} daily reports for week {} to {}",
                dailyReportsInWeek.size(), weekStart, weekEnd);

        DetectorReportChartVO weeklyAggregatedReport = combinePeriodReports(dailyReportsInWeek);
        calculateWeeklyMetrics(weeklyAggregatedReport);
        setWeeklyReportTimeBoundaries(weeklyAggregatedReport, weekStart, weekEnd);

        log.info("Successfully created weekly report for {} to {} with {} days of data",
                weekStart, weekEnd, dailyReportsInWeek.size());

        return Optional.of(weeklyAggregatedReport);
    }

    /**
     * Collects daily detector reports for each applicable day in the week.
     * Only processes days that are specified in the template's week days configuration.
     * Uses pre-parsed daily time period pairs for precise time range handling.
     *
     * @param template          The detector template containing week days configuration
     * @param weekStart         The start date of the week (Monday)
     * @param weekEnd           The end date of the week (Sunday)
     * @param dailyTimePairs    Map of daily time period pairs for all dates
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return List of daily reports for applicable days in the week
     */
    private List<DetectorReportChartVO> collectDailyReportsForWeek(
            DetectorTemplate template,
            LocalDate weekStart,
            LocalDate weekEnd,
            Map<LocalDate, Pair<LocalDateTime, LocalDateTime>> dailyTimePairs,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> dailyReports = new ArrayList<>();
        Set<DayOfWeek> configuredWeekDays = template.getWeekDays();
        LocalDate currentDate = weekStart;

        log.debug("Collecting daily reports for week {} to {}, configured days: {}, available time pairs: {}",
                weekStart, weekEnd, configuredWeekDays, dailyTimePairs.size());

        while (!currentDate.isAfter(weekEnd)) {
            DayOfWeek currentDayOfWeek = currentDate.getDayOfWeek();

            if (configuredWeekDays.contains(currentDayOfWeek)) {
                // Check if we have a time pair for this date
                Pair<LocalDateTime, LocalDateTime> timePair = dailyTimePairs.get(currentDate);

                if (timePair != null) {
                    log.debug("Processing {} ({}) with time pair: {} to {}",
                            currentDate, currentDayOfWeek, timePair.getFirst(), timePair.getSecond());

                    Optional<DetectorReportChartVO> dailyReport = collectDailyReportForDate(
                            currentDate, timePair, filterDetectorIds, agencyId, intersectionId);

                    if (dailyReport.isPresent()) {
                        dailyReports.add(dailyReport.get());
                        log.debug("Added daily report for {} with data", currentDate);
                    }
                } else {
                    log.debug("No time pair found for {} ({}), skipping", currentDate, currentDayOfWeek);
                }
            } else {
                log.debug("Skipping {} ({}) - not in configured week days", currentDate, currentDayOfWeek);
            }

            currentDate = currentDate.plusDays(DAY_INTERVAL);
        }

        log.debug("Collected {} daily reports for week {} to {}", dailyReports.size(), weekStart, weekEnd);
        return dailyReports;
    }

    /**
     * Collects detector report data for a single date using a pre-parsed time period pair.
     * Uses the provided start and end times directly from the time pair.
     * Handles potential errors gracefully and provides detailed logging.
     *
     * @param date              The date to collect data for
     * @param timePair          The time period pair containing start and end times for this date
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return Optional containing the daily report if successful, empty if no data or error
     */
    private Optional<DetectorReportChartVO> collectDailyReportForDate(
            LocalDate date,
            Pair<LocalDateTime, LocalDateTime> timePair,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        try {
            // Use the time pair directly - no need for extraction
            LocalDateTime fromTime = timePair.getFirst();
            LocalDateTime toTime = timePair.getSecond();

            log.debug("Analyzing detector data for {} ({}) from {} to {} (using time pair)",
                    date, date.getDayOfWeek(), fromTime, toTime);

            Optional<DetectorReportChartVO> result = detectorReportAnalysis(
                    agencyId, intersectionId, fromTime, toTime, filterDetectorIds);

            if (result.isEmpty()) {
                log.debug("No detector data found for {} ({})", date, date.getDayOfWeek());
            }

            return result;

        } catch (Exception e) {
            log.warn("Error collecting daily report for {} ({}): {}",
                    date, date.getDayOfWeek(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Calculates weekly metrics for all detectors and phases in the aggregated report.
     * This method triggers the weekly calculation logic for each phase data.
     *
     * @param report The aggregated weekly report to calculate metrics for
     */
    private void calculateWeeklyMetrics(DetectorReportChartVO report) {
        if (report.getDetectorDataMap() == null) {
            log.warn("No detector data map found in report, skipping weekly metrics calculation");
            return;
        }

        log.debug("Calculating weekly metrics for {} detectors", report.getDetectorDataMap().size());

        report.getDetectorDataMap().forEach((detectorId, detectorData) -> {
            if (detectorData.getDetectorPhaseDataMap() != null) {
                detectorData.getDetectorPhaseDataMap().forEach((phaseId, phaseData) -> {
                    log.debug("Calculating weekly metrics for detector {} phase {}", detectorId, phaseId);
                    phaseData.calculateReportWeekly();
                });
            }
        });

        log.debug("Completed weekly metrics calculation");
    }

    /**
     * Sets the time boundaries for the weekly aggregated report.
     * The report covers from the start of the week (Monday 00:00) to the end of the week (Sunday 23:59:59).
     *
     * @param report    The weekly report to set boundaries for
     * @param weekStart The Monday date of the week
     * @param weekEnd   The Sunday date of the week
     */
    private void setWeeklyReportTimeBoundaries(
            DetectorReportChartVO report,
            LocalDate weekStart,
            LocalDate weekEnd) {

        LocalDateTime fromTime = weekStart.atStartOfDay();
        LocalDateTime toTime = weekEnd.plusDays(1).atStartOfDay();

        report.setFromTime(fromTime);
        report.setToTime(toTime);

        log.debug("Set weekly report time boundaries: {} to {}", fromTime, toTime);
    }

    /**
     * Gets the start of the week (Monday) for a given date.
     * This is a utility method used for consistent week boundary calculation.
     *
     * @param date The date to find the week start for
     * @return The Monday of the week containing the given date
     */
    private LocalDate getWeekStart(LocalDate date) {
        return date.with(DayOfWeek.MONDAY);
    }

}
