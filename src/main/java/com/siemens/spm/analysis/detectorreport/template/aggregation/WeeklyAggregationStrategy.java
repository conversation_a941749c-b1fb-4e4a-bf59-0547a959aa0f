package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Strategy for weekly aggregation of detector reports.
 * Processes data in weekly intervals for days specified in the template.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
public class WeeklyAggregationStrategy extends BaseAggregationStrategy {

    private static final int DAY_INTERVAL = 1;

    public WeeklyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(
            DetectorTemplate template,
            IntersectionInternalVO intersection,
            List<LocalDateTime> targetDateTimes) {

        List<DetectorReportChartVO> resultCharts = createEmptyResultList();

        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        Integer agencyId = template.getAgencyId();
        String intersectionId = intersection.getId();

        List<Pair<LocalDateTime, LocalDateTime>> targetPeriods = splitPeriodIntoDaily(targetDateTimes);
        for (Pair<LocalDateTime, LocalDateTime> targetPeriod : targetPeriods) {
            LocalDateTime fromTime = targetPeriod.getFirst();
            LocalDateTime toTime = targetPeriod.getSecond();
            buildDailyResultCharts(fromTime, toTime, agencyId, intersectionId, filterDetectorIds, resultCharts);
        }

        // Group target date times by week and process each week
        for (LocalDateTime targetDateTime : targetDateTimes) {
            LocalDate targetDate = targetDateTime.toLocalDate();

            // Find the start of the week (Monday) for this target date
            LocalDate weekStart = targetDate.with(DayOfWeek.MONDAY);
            LocalDate weekEnd = calculateWeekEndDate(weekStart, targetDate);

            log.info("Processing weekly data for target date: {}, week range: {} to {}", targetDate, weekStart,
                    weekEnd);

            // Process the current week and add result to charts if data exists
            processWeek(template, weekStart, weekEnd, filterDetectorIds, agencyId, intersectionId)
                    .ifPresent(resultCharts::add);

            log.debug("End analysis weekly detector report for target date: {}", targetDate);
        }

        return resultCharts;
    }

    /**
     * Splits a period into daily sub-periods
     *
     * @param targetDateTimes The list of target date times
     * @return daily sub-periods for this period
     */
    private List<Pair<LocalDateTime, LocalDateTime>> splitPeriodIntoDaily(List<LocalDateTime> targetDateTimes) {
        Map<LocalDate, List<LocalDateTime>> groupedByDate = targetDateTimes.stream()
                .distinct()
                .collect(Collectors.groupingBy(
                                LocalDateTime::toLocalDate,
                                TreeMap::new,
                                Collectors.toList()
                        )
                );

        List<Pair<LocalDateTime, LocalDateTime>> dailyPeriods = new ArrayList<>();

        for (Map.Entry<LocalDate, List<LocalDateTime>> entry : groupedByDate.entrySet()) {
            List<LocalDateTime> dayHours = entry.getValue();
            dayHours.sort(null);
            LocalDateTime dayStart = dayHours.get(0);
            LocalDateTime dayEnd = dayHours.get(dayHours.size() - 1);
            dailyPeriods.add(Pair.of(dayStart, dayEnd));
        }
        return dailyPeriods;
    }

    /**
     * Calculates the end date for the current week period.
     * End date is either Sunday of current week or targetDate if it comes earlier.
     */
    private LocalDate calculateWeekEndDate(LocalDate weekStart, LocalDate targetDate) {
        LocalDate weekEnd = weekStart.with(DayOfWeek.SUNDAY);
        return weekEnd.isAfter(targetDate) ? targetDate : weekEnd;
    }

    /**
     * Processes a single week period, collecting and aggregating daily reports.
     *
     * @return Optional containing the aggregated report if data exists, empty otherwise
     */
    private Optional<DetectorReportChartVO> processWeek(
            DetectorTemplate template,
            LocalDate weekStart,
            LocalDate weekEnd,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> weeklyReports = collectDailyReportsForWeek(
                template, weekStart, weekEnd, filterDetectorIds, agencyId, intersectionId);

        if (weeklyReports.isEmpty()) {
            log.debug("No data found for calendar week {} to {} after applying week days filter",
                    weekStart, weekEnd);
            return Optional.empty();
        }

        log.debug("Creating aggregated report for calendar week {} to {}", weekStart, weekEnd);
        DetectorReportChartVO aggregatedReportPerWeek = combinePeriodReports(weeklyReports);

        // Calculate weekly metrics and set time boundaries
        calculateWeeklyMetrics(aggregatedReportPerWeek);
        setReportTimeBoundaries(aggregatedReportPerWeek, weekStart, weekEnd);

        log.debug("Added weekly report for period {} to {} with {} days of data",
                weekStart, weekEnd, weeklyReports.size());

        return Optional.of(aggregatedReportPerWeek);
    }

    /**
     * Collects daily detector reports for each applicable day in the week.
     */
    private List<DetectorReportChartVO> collectDailyReportsForWeek(
            DetectorTemplate template,
            LocalDate weekStart,
            LocalDate weekEnd,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> dailyReports = new ArrayList<>();
        LocalDate currentDate = weekStart;

        while (!currentDate.isAfter(weekEnd)) {
            collectDailyReport(template, currentDate, filterDetectorIds, agencyId, intersectionId)
                    .ifPresent(dailyReports::add);
            currentDate = currentDate.plusDays(DAY_INTERVAL);
        }

        return dailyReports;
    }

    /**
     * Collects detector report data for a single day.
     */
    private Optional<DetectorReportChartVO> collectDailyReport(
            DetectorTemplate template,
            LocalDate date,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        LocalDateTime fromTime = template.getStartTime().atDate(date);
        LocalDateTime toTime = template.getEndTime().atDate(date);

        log.debug("Analyzing detector data for {} from {} to {}", date.getDayOfWeek(), fromTime, toTime);
        return detectorReportAnalysis(agencyId, intersectionId, fromTime, toTime, filterDetectorIds);
    }

    /**
     * Calculates weekly metrics for all detectors and phases in the report.
     */
    private void calculateWeeklyMetrics(DetectorReportChartVO report) {
        report.getDetectorDataMap().forEach((detectorId, detectorData) ->
                detectorData.getDetectorPhaseDataMap().forEach((phaseId, phaseData) ->
                        phaseData.calculateReportWeekly()));
    }

    /**
     * Sets the time boundaries for the aggregated report.
     */
    private void setReportTimeBoundaries(
            DetectorReportChartVO report,
            LocalDate weekStart,
            LocalDate weekEnd) {
        report.setFromTime(weekStart.atStartOfDay());
        report.setToTime(weekEnd.plusDays(1).atStartOfDay());
    }

    /**
     * Builds daily result charts for a specific time range.
     */
    private void buildDailyResultCharts(LocalDateTime fromTime,
                                        LocalDateTime toTime,
                                        Integer agencyId,
                                        String intersectionId,
                                        List<Long> filterDetectorIds,
                                        List<DetectorReportChartVO> resultCharts) {
        log.info("Processing daily data with time range: {} to {}", fromTime, toTime);
        detectorReportAnalysis(agencyId, intersectionId, fromTime, toTime, filterDetectorIds).ifPresent(
                detectorReportChartVO -> {
                    detectorReportChartVO.setFromTime(fromTime);
                    detectorReportChartVO.setToTime(toTime);
                    resultCharts.add(detectorReportChartVO);
                });
        log.debug("End analysis daily detector report from {} to {}", fromTime, toTime);
    }

    /**
     * Groups a list of day periods into weekly periods (Monday to Sunday).
     * Each day period is represented as a pair of LocalDateTime objects (start and end times).
     *
     * @param dayPeriods List of day periods where each period is defined by start and end LocalDateTime
     * @return List of weekly buckets, where each bucket contains periods for one week (Monday to Sunday)
     */
    public List<List<Pair<LocalDateTime, LocalDateTime>>> groupDayPeriodsIntoWeeklyPeriods(
            List<Pair<LocalDateTime, LocalDateTime>> dayPeriods) {

        if (dayPeriods == null || dayPeriods.isEmpty()) {
            log.debug("No day periods to group into weekly periods");
            return new ArrayList<>();
        }

        log.debug("Grouping {} day periods into weekly periods", dayPeriods.size());

        // First, split any periods that span multiple days into single-day periods
        List<Pair<LocalDateTime, LocalDateTime>> normalizedPeriods = splitMultiDayPeriods(dayPeriods);

        // Group periods by week (Monday to Sunday)
        Map<LocalDate, List<Pair<LocalDateTime, LocalDateTime>>> weeklyGroups = normalizedPeriods.stream()
                .collect(Collectors.groupingBy(
                        period -> getWeekStart(period.getFirst().toLocalDate()),
                        TreeMap::new,
                        Collectors.toList()
                ));

        // Convert map to list of lists and sort periods within each week
        List<List<Pair<LocalDateTime, LocalDateTime>>> weeklyPeriods = new ArrayList<>();
        for (Map.Entry<LocalDate, List<Pair<LocalDateTime, LocalDateTime>>> entry : weeklyGroups.entrySet()) {
            LocalDate weekStart = entry.getKey();
            List<Pair<LocalDateTime, LocalDateTime>> weekPeriods = entry.getValue();

            // Sort periods within the week by start time
            weekPeriods.sort(Comparator.comparing(Pair::getFirst));

            weeklyPeriods.add(weekPeriods);

            log.debug("Week starting {} contains {} periods", weekStart, weekPeriods.size());
        }

        log.debug("Grouped day periods into {} weekly periods", weeklyPeriods.size());
        return weeklyPeriods;
    }

    /**
     * Splits periods that span multiple days into single-day periods.
     * This handles edge cases where a period crosses midnight or spans multiple days.
     *
     * @param dayPeriods Original list of day periods
     * @return List of normalized periods where each period is within a single day
     */
    private List<Pair<LocalDateTime, LocalDateTime>> splitMultiDayPeriods(
            List<Pair<LocalDateTime, LocalDateTime>> dayPeriods) {

        List<Pair<LocalDateTime, LocalDateTime>> normalizedPeriods = new ArrayList<>();

        for (Pair<LocalDateTime, LocalDateTime> period : dayPeriods) {
            LocalDateTime start = period.getFirst();
            LocalDateTime end = period.getSecond();

            if (start == null || end == null) {
                log.warn("Skipping period with null start or end time: start={}, end={}", start, end);
                continue;
            }

            if (start.isAfter(end)) {
                log.warn("Skipping period with start time after end time: start={}, end={}", start, end);
                continue;
            }

            // Check if period spans multiple days
            if (start.toLocalDate().equals(end.toLocalDate())) {
                // Period is within a single day
                normalizedPeriods.add(period);
            } else {
                // Period spans multiple days - split it
                normalizedPeriods.addAll(splitPeriodAcrossDays(start, end));
            }
        }

        return normalizedPeriods;
    }

    /**
     * Splits a period that spans multiple days into separate daily periods.
     *
     * @param start Start time of the period
     * @param end   End time of the period
     * @return List of daily periods
     */
    private List<Pair<LocalDateTime, LocalDateTime>> splitPeriodAcrossDays(
            LocalDateTime start, LocalDateTime end) {

        List<Pair<LocalDateTime, LocalDateTime>> dailyPeriods = new ArrayList<>();
        LocalDateTime currentStart = start;

        while (currentStart.toLocalDate().isBefore(end.toLocalDate())) {
            // End of current day
            LocalDateTime currentEnd = currentStart.toLocalDate().plusDays(1).atStartOfDay();
            dailyPeriods.add(Pair.of(currentStart, currentEnd));

            // Start of next day
            currentStart = currentEnd;
        }

        // Add the final period for the last day
        if (!currentStart.isAfter(end)) {
            dailyPeriods.add(Pair.of(currentStart, end));
        }

        log.debug("Split period from {} to {} into {} daily periods", start, end, dailyPeriods.size());
        return dailyPeriods;
    }

    /**
     * Gets the start of the week (Monday) for a given date.
     *
     * @param date The date to find the week start for
     * @return The Monday of the week containing the given date
     */
    private LocalDate getWeekStart(LocalDate date) {
        return date.with(DayOfWeek.MONDAY);
    }

}
